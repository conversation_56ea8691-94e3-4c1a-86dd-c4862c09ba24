// 最小化BPF网络策略控制程序
// 避免复杂的头文件依赖

#include <linux/bpf.h>
#include <bpf/bpf_helpers.h>

// 基本类型定义
typedef unsigned char u8;
typedef unsigned short u16;
typedef unsigned int u32;
typedef unsigned long long u64;

// 简化的socket地址结构
struct bpf_sock_addr {
    u32 user_family;
    u32 user_ip4;
    u32 user_ip6[4];
    u32 user_port;
    u32 family;
    u32 type;
    u32 protocol;
    u32 msg_src_ip4;
    u32 msg_src_ip6[4];
};

// 端口策略映射
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, u16);
    __type(value, u32);
    __uint(max_entries, 256);
} port_policy_map SEC(".maps");

// 进程路径白名单
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, u64);
    __type(value, u8);
    __uint(max_entries, 2048);
} process_path_allow_map SEC(".maps");

// 进程路径黑名单
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, u64);
    __type(value, u8);
    __uint(max_entries, 2048);
} process_path_deny_map SEC(".maps");

// IP白名单
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, u64);
    __type(value, u8);
    __uint(max_entries, 1024);
} cidr_allow_map SEC(".maps");

// IP黑名单
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, u64);
    __type(value, u8);
    __uint(max_entries, 1024);
} cidr_deny_map SEC(".maps");

// 统计信息
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, u32);
    __type(value, u64);
    __uint(max_entries, 10);
} policy_stats SEC(".maps");

// 字符串哈希函数
static inline u32 hash_string(char *str, int max_len) {
    u32 hash = 5381;
    for (int i = 0; i < max_len && str[i] != 0; i++) {
        hash = ((hash << 5) + hash) + str[i];
    }
    return hash;
}

// 网络字节序转换
static inline u16 ntohs(u16 netshort) {
    return (netshort << 8) | (netshort >> 8);
}

// 获取进程名
static inline int get_process_name(char *name_buf, int buf_size) {
    return bpf_get_current_comm(name_buf, buf_size);
}

// 主策略检查函数
static inline int check_policy(struct bpf_sock_addr *ctx) {
    // 获取连接信息
    u16 dst_port = ntohs(ctx->user_port);
    u32 src_ip = ctx->user_ip4;
    
    // 获取进程名并计算哈希
    char proc_name[16];
    u32 name_hash = 0;
    if (get_process_name(proc_name, sizeof(proc_name)) == 0) {
        name_hash = hash_string(proc_name, sizeof(proc_name));
    }
    
    // 更新总连接数统计
    u32 total_key = 0;
    u64 *total_count = bpf_map_lookup_elem(&policy_stats, &total_key);
    if (total_count) {
        (*total_count)++;
    } else {
        u64 init_val = 1;
        bpf_map_update_elem(&policy_stats, &total_key, &init_val, BPF_ANY);
    }
    
    // 查找端口对应的策略ID
    u32 *policy_id_ptr = bpf_map_lookup_elem(&port_policy_map, &dst_port);
    if (!policy_id_ptr) {
        // 端口不在策略中，默认拒绝
        u32 denied_key = 2;
        u64 *denied_count = bpf_map_lookup_elem(&policy_stats, &denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            u64 init_val = 1;
            bpf_map_update_elem(&policy_stats, &denied_key, &init_val, BPF_ANY);
        }
        return 0;
    }
    
    u32 policy_id = *policy_id_ptr;
    
    // 检查进程名黑名单
    if (name_hash != 0) {
        u64 process_deny_key = ((u64)policy_id << 32) | name_hash;
        u8 *process_denied = bpf_map_lookup_elem(&process_path_deny_map, &process_deny_key);
        if (process_denied && *process_denied == 1) {
            // 进程在黑名单中，拒绝连接
            u32 denied_key = 2;
            u64 *denied_count = bpf_map_lookup_elem(&policy_stats, &denied_key);
            if (denied_count) {
                (*denied_count)++;
            } else {
                u64 init_val = 1;
                bpf_map_update_elem(&policy_stats, &denied_key, &init_val, BPF_ANY);
            }
            return 0;
        }
    }
    
    // 检查IP黑名单
    u64 ip_deny_key = ((u64)policy_id << 32) | src_ip;
    u8 *ip_denied = bpf_map_lookup_elem(&cidr_deny_map, &ip_deny_key);
    if (ip_denied && *ip_denied == 1) {
        // IP在黑名单中，拒绝连接
        u32 denied_key = 2;
        u64 *denied_count = bpf_map_lookup_elem(&policy_stats, &denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            u64 init_val = 1;
            bpf_map_update_elem(&policy_stats, &denied_key, &init_val, BPF_ANY);
        }
        return 0;
    }
    
    // 检查进程名白名单
    u8 process_in_whitelist = 0;
    if (name_hash != 0) {
        u64 process_allow_key = ((u64)policy_id << 32) | name_hash;
        u8 *process_allowed = bpf_map_lookup_elem(&process_path_allow_map, &process_allow_key);
        process_in_whitelist = (process_allowed && *process_allowed == 1) ? 1 : 0;
    }
    
    // 检查IP白名单
    u64 ip_allow_key = ((u64)policy_id << 32) | src_ip;
    u8 *ip_allowed = bpf_map_lookup_elem(&cidr_allow_map, &ip_allow_key);
    u8 ip_in_whitelist = (ip_allowed && *ip_allowed == 1) ? 1 : 0;

    // 决策逻辑：进程在白名单 AND IP在白名单
    if (process_in_whitelist && ip_in_whitelist) {
        // 允许连接
        u32 allowed_key = 1;
        u64 *allowed_count = bpf_map_lookup_elem(&policy_stats, &allowed_key);
        if (allowed_count) {
            (*allowed_count)++;
        } else {
            u64 init_val = 1;
            bpf_map_update_elem(&policy_stats, &allowed_key, &init_val, BPF_ANY);
        }
        return 1;
    } else {
        // 拒绝连接
        u32 denied_key = 2;
        u64 *denied_count = bpf_map_lookup_elem(&policy_stats, &denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            u64 init_val = 1;
            bpf_map_update_elem(&policy_stats, &denied_key, &init_val, BPF_ANY);
        }
        return 0;
    }
}

// cgroup/connect4入口点
SEC("cgroup/connect4")
int cgroup_connect4(struct bpf_sock_addr *ctx) {
    return check_policy(ctx);
}

// cgroup/connect6入口点
SEC("cgroup/connect6")
int cgroup_connect6(struct bpf_sock_addr *ctx) {
    return check_policy(ctx);
}

char _license[] SEC("license") = "GPL";
