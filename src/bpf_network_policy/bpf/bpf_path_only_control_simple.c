// 基于进程路径的BPF网络策略控制程序 - 简化版本
// 专门针对进程路径管控，不包含进程名管控

#include <linux/bpf.h>
#include <bpf/bpf_helpers.h>

// 基本类型定义
typedef unsigned char __u8;
typedef unsigned short __u16;
typedef unsigned int __u32;
typedef unsigned long long __u64;

// BPF socket address structure (simplified)
struct bpf_sock_addr {
    __u32 user_family;
    __u32 user_ip4;
    __u32 user_ip6[4];
    __u32 user_port;
    __u32 family;
    __u32 type;
    __u32 protocol;
    __u32 msg_src_ip4;
    __u32 msg_src_ip6[4];
};

// 端口策略映射 - 每个端口对应一个策略ID
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u16);
    __type(value, __u32);
    __uint(max_entries, 256);
} port_policy_map SEC(".maps");

// 进程路径白名单 - key: policy_id << 32 | process_path_hash, value: 1
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u64);
    __type(value, __u8);
    __uint(max_entries, 2048);
} process_path_allow_map SEC(".maps");

// 进程路径黑名单 - key: policy_id << 32 | process_path_hash, value: 1
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u64);
    __type(value, __u8);
    __uint(max_entries, 2048);
} process_path_deny_map SEC(".maps");

// IP白名单 - key: policy_id << 32 | ip_addr, value: 1
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u64);
    __type(value, __u8);
    __uint(max_entries, 1024);
} cidr_allow_map SEC(".maps");

// IP黑名单 - key: policy_id << 32 | ip_addr, value: 1
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u64);
    __type(value, __u8);
    __uint(max_entries, 1024);
} cidr_deny_map SEC(".maps");

// 统计信息
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u32);
    __type(value, __u64);
    __uint(max_entries, 10);
} policy_stats SEC(".maps");

// 增强的字符串哈希函数，支持长路径
static inline __u32 hash_long_string(char *str, int max_len) {
    __u32 hash = 5381;
    for (int i = 0; i < max_len && str[i] != 0; i++) {
        hash = ((hash << 5) + hash) + str[i];
    }
    return hash;
}

// 获取进程可执行文件路径的辅助函数
static inline int get_process_path(char *path_buf, int buf_size) {
    // 方法1：尝试使用进程名作为后备方案
    // 注意：这不是完整路径，只是进程名，但在某些情况下可以工作
    char comm[16];
    int ret = bpf_get_current_comm(comm, sizeof(comm));
    if (ret != 0) {
        return -1;
    }

    // 将进程名复制到路径缓冲区
    // 注意：这不是完整路径，只是进程名
    int len = 0;
    for (int i = 0; i < 15 && i < buf_size - 1 && comm[i] != 0; i++) {
        path_buf[i] = comm[i];
        len++;
    }
    path_buf[len] = 0;

    return len;
}

// 尝试从/proc/self/exe获取进程路径（实验性功能）
static inline int get_process_exe_path(char *path_buf, int buf_size) {
    // 这是一个实验性的方法，可能在某些内核版本中不工作
    // 目前BPF对文件系统访问有限制，所以这个函数主要是占位符
    
    // 作为后备，使用进程名
    return get_process_path(path_buf, buf_size);
}

// 网络字节序转换
static inline __u16 bpf_ntohs(__u16 netshort) {
    return (netshort << 8) | (netshort >> 8);
}

// 主要的策略检查函数 - 仅基于进程路径
static inline int check_connection_policy_path_only(struct bpf_sock_addr *ctx) {
    // 获取连接信息
    __u16 dst_port = bpf_ntohs(ctx->user_port);
    __u32 src_ip = ctx->user_ip4;
    __u32 pid = bpf_get_current_pid_tgid() >> 32;

    // 获取进程路径
    char proc_path[256];
    __u32 path_hash = 0;
    int path_ret = get_process_exe_path(proc_path, sizeof(proc_path));
    if (path_ret >= 0) {
        path_hash = hash_long_string(proc_path, sizeof(proc_path));
    }
    
    // 更新总连接数统计
    __u32 total_key = 0;
    __u64 *total_count = bpf_map_lookup_elem(&policy_stats, &total_key);
    if (total_count) {
        (*total_count)++;
    } else {
        __u64 init_val = 1;
        bpf_map_update_elem(&policy_stats, &total_key, &init_val, BPF_ANY);
    }
    
    // 1. 查找端口对应的策略ID
    __u32 *policy_id_ptr = bpf_map_lookup_elem(&port_policy_map, &dst_port);
    if (!policy_id_ptr) {
        // 端口不在任何策略中，默认拒绝
        __u32 denied_key = 2;
        __u64 *denied_count = bpf_map_lookup_elem(&policy_stats, &denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            __u64 init_val = 1;
            bpf_map_update_elem(&policy_stats, &denied_key, &init_val, BPF_ANY);
        }
        return 0;
    }
    
    __u32 policy_id = *policy_id_ptr;
    
    // 2. 检查进程路径黑名单
    if (path_hash != 0) {
        __u64 process_path_deny_key = ((__u64)policy_id << 32) | path_hash;
        __u8 *process_path_denied = bpf_map_lookup_elem(&process_path_deny_map, &process_path_deny_key);
        if (process_path_denied && *process_path_denied == 1) {
            // 进程路径在黑名单中，拒绝连接
            __u32 denied_key = 2;
            __u64 *denied_count = bpf_map_lookup_elem(&policy_stats, &denied_key);
            if (denied_count) {
                (*denied_count)++;
            } else {
                __u64 init_val = 1;
                bpf_map_update_elem(&policy_stats, &denied_key, &init_val, BPF_ANY);
            }
            return 0;
        }
    }
    
    // 3. 检查IP黑名单
    __u64 ip_deny_key = ((__u64)policy_id << 32) | src_ip;
    __u8 *ip_denied = bpf_map_lookup_elem(&cidr_deny_map, &ip_deny_key);
    if (ip_denied && *ip_denied == 1) {
        // IP在黑名单中，拒绝连接
        __u32 denied_key = 2;
        __u64 *denied_count = bpf_map_lookup_elem(&policy_stats, &denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            __u64 init_val = 1;
            bpf_map_update_elem(&policy_stats, &denied_key, &init_val, BPF_ANY);
        }
        return 0;
    }
    
    // 4. 检查进程路径白名单
    __u8 process_path_in_whitelist = 0;
    if (path_hash != 0) {
        __u64 process_path_allow_key = ((__u64)policy_id << 32) | path_hash;
        __u8 *process_path_allowed = bpf_map_lookup_elem(&process_path_allow_map, &process_path_allow_key);
        process_path_in_whitelist = (process_path_allowed && *process_path_allowed == 1) ? 1 : 0;
    }
    
    // 5. 检查IP白名单
    __u64 ip_allow_key = ((__u64)policy_id << 32) | src_ip;
    __u8 *ip_allowed = bpf_map_lookup_elem(&cidr_allow_map, &ip_allow_key);
    __u8 ip_in_whitelist = (ip_allowed && *ip_allowed == 1) ? 1 : 0;

    // 6. 决策逻辑：进程路径在白名单 AND IP在白名单
    if (process_path_in_whitelist && ip_in_whitelist) {
        // 允许连接
        __u32 allowed_key = 1;
        __u64 *allowed_count = bpf_map_lookup_elem(&policy_stats, &allowed_key);
        if (allowed_count) {
            (*allowed_count)++;
        } else {
            __u64 init_val = 1;
            bpf_map_update_elem(&policy_stats, &allowed_key, &init_val, BPF_ANY);
        }
        return 1;
    } else {
        // 拒绝连接
        __u32 denied_key = 2;
        __u64 *denied_count = bpf_map_lookup_elem(&policy_stats, &denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            __u64 init_val = 1;
            bpf_map_update_elem(&policy_stats, &denied_key, &init_val, BPF_ANY);
        }
        return 0;
    }
}

// 用于cgroup/connect4的入口点
SEC("cgroup/connect4")
int cgroup_connect4(struct bpf_sock_addr *ctx) {
    return check_connection_policy_path_only(ctx);
}

// 用于cgroup/connect6的入口点  
SEC("cgroup/connect6")
int cgroup_connect6(struct bpf_sock_addr *ctx) {
    return check_connection_policy_path_only(ctx);
}

// 用于监控的入口点（不拦截，只记录）
SEC("cgroup/connect4")
int trace_connect(struct bpf_sock_addr *ctx) {
    check_connection_policy_path_only(ctx);
    return 1; // 总是允许，只用于监控
}

char _license[] SEC("license") = "GPL";
