// 完全自包含的BPF网络策略控制程序
// 不依赖外部头文件

// BPF系统调用和常量定义
#define BPF_MAP_TYPE_HASH 1
#define BPF_ANY 0

// 基本类型定义
typedef unsigned char __u8;
typedef unsigned short __u16;
typedef unsigned int __u32;
typedef unsigned long long __u64;

// BPF辅助函数声明
static long (*bpf_map_lookup_elem)(void *map, const void *key) = (void *) 1;
static long (*bpf_map_update_elem)(void *map, const void *key, const void *value, __u64 flags) = (void *) 2;
static long (*bpf_get_current_pid_tgid)(void) = (void *) 14;
static long (*bpf_get_current_comm)(void *buf, __u32 size_of_buf) = (void *) 16;

// 简化的socket地址结构
struct bpf_sock_addr {
    __u32 user_family;
    __u32 user_ip4;
    __u32 user_ip6[4];
    __u32 user_port;
    __u32 family;
    __u32 type;
    __u32 protocol;
    __u32 msg_src_ip4;
    __u32 msg_src_ip6[4];
};

// 映射定义宏
#define SEC(name) __attribute__((section(name), used))
#define __uint(name, val) int (*name)[val]
#define __type(name, val) typeof(val) *name

// 端口策略映射
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u16);
    __type(value, __u32);
    __uint(max_entries, 256);
} port_policy_map SEC(".maps");

// 进程路径白名单
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u64);
    __type(value, __u8);
    __uint(max_entries, 2048);
} process_path_allow_map SEC(".maps");

// 进程路径黑名单
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u64);
    __type(value, __u8);
    __uint(max_entries, 2048);
} process_path_deny_map SEC(".maps");

// IP白名单
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u64);
    __type(value, __u8);
    __uint(max_entries, 1024);
} cidr_allow_map SEC(".maps");

// IP黑名单
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u64);
    __type(value, __u8);
    __uint(max_entries, 1024);
} cidr_deny_map SEC(".maps");

// 统计信息
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, __u32);
    __type(value, __u64);
    __uint(max_entries, 10);
} policy_stats SEC(".maps");

// 字符串哈希函数
static inline __u32 hash_string(char *str, int max_len) {
    __u32 hash = 5381;
    for (int i = 0; i < max_len && str[i] != 0; i++) {
        hash = ((hash << 5) + hash) + str[i];
    }
    return hash;
}

// 网络字节序转换
static inline __u16 ntohs(__u16 netshort) {
    return (netshort << 8) | (netshort >> 8);
}

// 获取进程名
static inline int get_process_name(char *name_buf, int buf_size) {
    return bpf_get_current_comm(name_buf, buf_size);
}

// 主策略检查函数
static inline int check_policy(struct bpf_sock_addr *ctx) {
    // 获取连接信息
    __u16 dst_port = ntohs(ctx->user_port);
    __u32 src_ip = ctx->user_ip4;
    
    // 获取进程名并计算哈希
    char proc_name[16];
    __u32 name_hash = 0;
    if (get_process_name(proc_name, sizeof(proc_name)) == 0) {
        name_hash = hash_string(proc_name, sizeof(proc_name));
    }
    
    // 更新总连接数统计
    __u32 total_key = 0;
    __u64 *total_count = bpf_map_lookup_elem(&policy_stats, &total_key);
    if (total_count) {
        (*total_count)++;
    } else {
        __u64 init_val = 1;
        bpf_map_update_elem(&policy_stats, &total_key, &init_val, BPF_ANY);
    }
    
    // 查找端口对应的策略ID
    __u32 *policy_id_ptr = bpf_map_lookup_elem(&port_policy_map, &dst_port);
    if (!policy_id_ptr) {
        // 端口不在策略中，默认拒绝
        __u32 denied_key = 2;
        __u64 *denied_count = bpf_map_lookup_elem(&policy_stats, &denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            __u64 init_val = 1;
            bpf_map_update_elem(&policy_stats, &denied_key, &init_val, BPF_ANY);
        }
        return 0;
    }
    
    __u32 policy_id = *policy_id_ptr;
    
    // 检查进程名黑名单
    if (name_hash != 0) {
        __u64 process_deny_key = ((__u64)policy_id << 32) | name_hash;
        __u8 *process_denied = bpf_map_lookup_elem(&process_path_deny_map, &process_deny_key);
        if (process_denied && *process_denied == 1) {
            // 进程在黑名单中，拒绝连接
            __u32 denied_key = 2;
            __u64 *denied_count = bpf_map_lookup_elem(&policy_stats, &denied_key);
            if (denied_count) {
                (*denied_count)++;
            } else {
                __u64 init_val = 1;
                bpf_map_update_elem(&policy_stats, &denied_key, &init_val, BPF_ANY);
            }
            return 0;
        }
    }
    
    // 检查IP黑名单
    __u64 ip_deny_key = ((__u64)policy_id << 32) | src_ip;
    __u8 *ip_denied = bpf_map_lookup_elem(&cidr_deny_map, &ip_deny_key);
    if (ip_denied && *ip_denied == 1) {
        // IP在黑名单中，拒绝连接
        __u32 denied_key = 2;
        __u64 *denied_count = bpf_map_lookup_elem(&policy_stats, &denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            __u64 init_val = 1;
            bpf_map_update_elem(&policy_stats, &denied_key, &init_val, BPF_ANY);
        }
        return 0;
    }
    
    // 检查进程名白名单
    __u8 process_in_whitelist = 0;
    if (name_hash != 0) {
        __u64 process_allow_key = ((__u64)policy_id << 32) | name_hash;
        __u8 *process_allowed = bpf_map_lookup_elem(&process_path_allow_map, &process_allow_key);
        process_in_whitelist = (process_allowed && *process_allowed == 1) ? 1 : 0;
    }
    
    // 检查IP白名单
    __u64 ip_allow_key = ((__u64)policy_id << 32) | src_ip;
    __u8 *ip_allowed = bpf_map_lookup_elem(&cidr_allow_map, &ip_allow_key);
    __u8 ip_in_whitelist = (ip_allowed && *ip_allowed == 1) ? 1 : 0;

    // 决策逻辑：进程在白名单 AND IP在白名单
    if (process_in_whitelist && ip_in_whitelist) {
        // 允许连接
        __u32 allowed_key = 1;
        __u64 *allowed_count = bpf_map_lookup_elem(&policy_stats, &allowed_key);
        if (allowed_count) {
            (*allowed_count)++;
        } else {
            __u64 init_val = 1;
            bpf_map_update_elem(&policy_stats, &allowed_key, &init_val, BPF_ANY);
        }
        return 1;
    } else {
        // 拒绝连接
        __u32 denied_key = 2;
        __u64 *denied_count = bpf_map_lookup_elem(&policy_stats, &denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            __u64 init_val = 1;
            bpf_map_update_elem(&policy_stats, &denied_key, &init_val, BPF_ANY);
        }
        return 0;
    }
}

// cgroup/connect4入口点
SEC("cgroup/connect4")
int cgroup_connect4(struct bpf_sock_addr *ctx) {
    return check_policy(ctx);
}

// cgroup/connect6入口点
SEC("cgroup/connect6")
int cgroup_connect6(struct bpf_sock_addr *ctx) {
    return check_policy(ctx);
}

char _license[] SEC("license") = "GPL";
