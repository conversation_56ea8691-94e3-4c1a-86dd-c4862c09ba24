# Makefile for BPF Path Policy Controller (Pure C)

CC = gcc
CLANG = clang
CFLAGS = -Wall -Wextra -O2 -g
BPF_CFLAGS = -O2 -target bpf -c -nostdinc

# 库依赖
LIBS = -lbpf

# 源文件
USER_SRC = path_policy_control.c
BPF_SRC = ../bpf/bpf_standalone.c
BPF_OBJ = ../bpf/bpf_standalone.o

# 目标文件
TARGET = path_policy_control

# 默认目标
all: $(TARGET)

# 编译eBPF程序
$(BPF_OBJ): $(BPF_SRC)
	@echo "Compiling eBPF program..."
	$(CLANG) $(BPF_CFLAGS) -o $@ $<

# 编译用户空间程序
$(TARGET): $(USER_SRC) $(BPF_OBJ)
	@echo "Compiling user space program..."
	$(CC) $(CFLAGS) -o $@ $< $(LIBS)

# 清理
clean:
	rm -f $(TARGET) $(BPF_OBJ)

# 安装依赖（Ubuntu/Debian）
install-deps:
	@echo "Installing dependencies..."
	sudo apt-get update
	sudo apt-get install -y \
		libbpf-dev \
		clang \
		llvm \
		linux-headers-$(shell uname -r)

# 运行程序
run: $(TARGET)
	sudo ./$(TARGET)

# 检查BPF程序
check-bpf: $(BPF_OBJ)
	@echo "Checking BPF object file..."
	file $(BPF_OBJ)
	readelf -h $(BPF_OBJ)

.PHONY: all clean install-deps run check-bpf
