/*
 * 基于进程路径的BPF网络策略控制程序 - 纯C语言用户空间实现
 * 不使用BCC框架，直接使用libbpf
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <arpa/inet.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>
// #include <yaml.h>  // 暂时不使用YAML，使用硬编码配置

#define MAX_PORTS 256
#define MAX_PATHS 2048
#define MAX_IPS 1024
#define MAX_PATH_LEN 256
#define MAX_CIDR_LEN 32

// 策略配置结构
struct policy_config {
    int policy_id;
    char **process_path_allow_list;
    int process_path_allow_count;
    char **process_path_deny_list;
    int process_path_deny_count;
    char **cidr_allow_list;
    int cidr_allow_count;
    char **cidr_deny_list;
    int cidr_deny_count;
};

// 全局变量
static struct bpf_object *obj = NULL;
static struct bpf_program *prog = NULL;
static int prog_fd = -1;
static int port_policy_map_fd = -1;
static int process_path_allow_map_fd = -1;
static int process_path_deny_map_fd = -1;
static int cidr_allow_map_fd = -1;
static int cidr_deny_map_fd = -1;
static int policy_stats_fd = -1;

static struct policy_config policies[MAX_PORTS];
static int policy_count = 0;
static volatile int running = 1;

// 信号处理函数
static void signal_handler(int sig) {
    printf("\nReceived signal %d, shutting down...\n", sig);
    running = 0;
}

// 字符串哈希函数（与eBPF中的保持一致）
static unsigned int hash_long_string(const char *str) {
    unsigned int hash = 5381;
    int c;
    while ((c = *str++)) {
        hash = ((hash << 5) + hash) + c;
    }
    return hash;
}

// IP地址转换为整数
static unsigned int ip_to_int(const char *ip_str) {
    struct in_addr addr;
    if (inet_aton(ip_str, &addr) == 0) {
        return 0;
    }
    return ntohl(addr.s_addr);
}

// 解析CIDR并返回网络地址
static unsigned int parse_cidr(const char *cidr_str) {
    char ip_str[INET_ADDRSTRLEN];
    char *slash = strchr(cidr_str, '/');
    
    if (slash) {
        size_t ip_len = slash - cidr_str;
        if (ip_len >= sizeof(ip_str)) {
            return 0;
        }
        strncpy(ip_str, cidr_str, ip_len);
        ip_str[ip_len] = '\0';
    } else {
        strncpy(ip_str, cidr_str, sizeof(ip_str) - 1);
        ip_str[sizeof(ip_str) - 1] = '\0';
    }
    
    return ip_to_int(ip_str);
}

// 加载BPF程序
static int load_bpf_program(const char *filename) {
    printf("Loading BPF program from %s...\n", filename);
    
    // 打开BPF对象文件
    obj = bpf_object__open(filename);
    if (libbpf_get_error(obj)) {
        fprintf(stderr, "Failed to open BPF object file: %s\n", strerror(errno));
        return -1;
    }
    
    // 加载BPF程序
    if (bpf_object__load(obj)) {
        fprintf(stderr, "Failed to load BPF object: %s\n", strerror(errno));
        bpf_object__close(obj);
        return -1;
    }
    
    // 获取程序
    prog = bpf_object__find_program_by_name(obj, "cgroup_connect4");
    if (!prog) {
        fprintf(stderr, "Failed to find BPF program 'cgroup_connect4'\n");
        bpf_object__close(obj);
        return -1;
    }
    
    prog_fd = bpf_program__fd(prog);
    if (prog_fd < 0) {
        fprintf(stderr, "Failed to get BPF program fd\n");
        bpf_object__close(obj);
        return -1;
    }
    
    // 获取映射文件描述符
    struct bpf_map *map;
    
    map = bpf_object__find_map_by_name(obj, "port_policy_map");
    if (!map) {
        fprintf(stderr, "Failed to find port_policy_map\n");
        bpf_object__close(obj);
        return -1;
    }
    port_policy_map_fd = bpf_map__fd(map);
    
    map = bpf_object__find_map_by_name(obj, "process_path_allow_map");
    if (!map) {
        fprintf(stderr, "Failed to find process_path_allow_map\n");
        bpf_object__close(obj);
        return -1;
    }
    process_path_allow_map_fd = bpf_map__fd(map);
    
    map = bpf_object__find_map_by_name(obj, "process_path_deny_map");
    if (!map) {
        fprintf(stderr, "Failed to find process_path_deny_map\n");
        bpf_object__close(obj);
        return -1;
    }
    process_path_deny_map_fd = bpf_map__fd(map);
    
    map = bpf_object__find_map_by_name(obj, "cidr_allow_map");
    if (!map) {
        fprintf(stderr, "Failed to find cidr_allow_map\n");
        bpf_object__close(obj);
        return -1;
    }
    cidr_allow_map_fd = bpf_map__fd(map);
    
    map = bpf_object__find_map_by_name(obj, "cidr_deny_map");
    if (!map) {
        fprintf(stderr, "Failed to find cidr_deny_map\n");
        bpf_object__close(obj);
        return -1;
    }
    cidr_deny_map_fd = bpf_map__fd(map);
    
    map = bpf_object__find_map_by_name(obj, "policy_stats");
    if (!map) {
        fprintf(stderr, "Failed to find policy_stats\n");
        bpf_object__close(obj);
        return -1;
    }
    policy_stats_fd = bpf_map__fd(map);
    
    printf("✓ BPF program loaded successfully\n");
    return 0;
}

// 清理资源
static void cleanup() {
    if (obj) {
        bpf_object__close(obj);
        obj = NULL;
    }
    
    // 清理策略配置内存
    for (int i = 0; i < policy_count; i++) {
        if (policies[i].process_path_allow_list) {
            for (int j = 0; j < policies[i].process_path_allow_count; j++) {
                free(policies[i].process_path_allow_list[j]);
            }
            free(policies[i].process_path_allow_list);
        }
        if (policies[i].process_path_deny_list) {
            for (int j = 0; j < policies[i].process_path_deny_count; j++) {
                free(policies[i].process_path_deny_list[j]);
            }
            free(policies[i].process_path_deny_list);
        }
        if (policies[i].cidr_allow_list) {
            for (int j = 0; j < policies[i].cidr_allow_count; j++) {
                free(policies[i].cidr_allow_list[j]);
            }
            free(policies[i].cidr_allow_list);
        }
        if (policies[i].cidr_deny_list) {
            for (int j = 0; j < policies[i].cidr_deny_count; j++) {
                free(policies[i].cidr_deny_list[j]);
            }
            free(policies[i].cidr_deny_list);
        }
    }
}

// 配置示例策略（硬编码）
static int configure_example_policies() {
    printf("Configuring example policies...\n");

    // 示例策略：端口8080，允许/usr/bin/curl，允许127.0.0.1
    unsigned short port = 8080;
    unsigned int policy_id = 1;

    // 1. 配置端口到策略ID的映射
    if (bpf_map_update_elem(port_policy_map_fd, &port, &policy_id, BPF_ANY) != 0) {
        fprintf(stderr, "Failed to update port policy map: %s\n", strerror(errno));
        return -1;
    }
    printf("  ✓ Configured port %d -> policy %d\n", port, policy_id);

    // 2. 配置进程路径白名单
    const char *allowed_path = "/usr/bin/curl";
    unsigned int path_hash = hash_long_string(allowed_path);
    unsigned long long path_key = ((unsigned long long)policy_id << 32) | path_hash;
    unsigned char allow_value = 1;

    if (bpf_map_update_elem(process_path_allow_map_fd, &path_key, &allow_value, BPF_ANY) != 0) {
        fprintf(stderr, "Failed to update process path allow map: %s\n", strerror(errno));
        return -1;
    }
    printf("  ✓ Added path allow: %s (hash: 0x%x)\n", allowed_path, path_hash);

    // 3. 配置IP白名单
    unsigned int allowed_ip = ip_to_int("127.0.0.1");
    unsigned long long ip_key = ((unsigned long long)policy_id << 32) | allowed_ip;

    if (bpf_map_update_elem(cidr_allow_map_fd, &ip_key, &allow_value, BPF_ANY) != 0) {
        fprintf(stderr, "Failed to update CIDR allow map: %s\n", strerror(errno));
        return -1;
    }
    printf("  ✓ Added IP allow: 127.0.0.1 (0x%x)\n", allowed_ip);

    // 示例策略2：端口9090，拒绝/usr/bin/wget
    port = 9090;
    policy_id = 2;

    // 配置端口到策略ID的映射
    if (bpf_map_update_elem(port_policy_map_fd, &port, &policy_id, BPF_ANY) != 0) {
        fprintf(stderr, "Failed to update port policy map: %s\n", strerror(errno));
        return -1;
    }
    printf("  ✓ Configured port %d -> policy %d\n", port, policy_id);

    // 配置进程路径黑名单
    const char *denied_path = "/usr/bin/wget";
    path_hash = hash_long_string(denied_path);
    path_key = ((unsigned long long)policy_id << 32) | path_hash;

    if (bpf_map_update_elem(process_path_deny_map_fd, &path_key, &allow_value, BPF_ANY) != 0) {
        fprintf(stderr, "Failed to update process path deny map: %s\n", strerror(errno));
        return -1;
    }
    printf("  ✓ Added path deny: %s (hash: 0x%x)\n", denied_path, path_hash);

    // 配置IP白名单（允许所有IP除了被拒绝的进程）
    allowed_ip = ip_to_int("0.0.0.0");  // 作为通配符
    ip_key = ((unsigned long long)policy_id << 32) | allowed_ip;

    if (bpf_map_update_elem(cidr_allow_map_fd, &ip_key, &allow_value, BPF_ANY) != 0) {
        fprintf(stderr, "Failed to update CIDR allow map: %s\n", strerror(errno));
        return -1;
    }
    printf("  ✓ Added IP allow: 0.0.0.0 (wildcard)\n");

    printf("✓ Example policies configured successfully\n");
    return 0;
}

// 显示策略摘要
static void show_policy_summary() {
    printf("\n=== Policy Configuration Summary ===\n");
    printf("Configured policies:\n");
    printf("  Port 8080 (Policy 1):\n");
    printf("    - Allow process: /usr/bin/curl\n");
    printf("    - Allow IP: 127.0.0.1\n");
    printf("  Port 9090 (Policy 2):\n");
    printf("    - Deny process: /usr/bin/wget\n");
    printf("    - Allow IP: 0.0.0.0 (wildcard)\n");
}

// 显示统计信息
static void show_statistics() {
    printf("\n=== Policy Execution Statistics ===\n");

    unsigned int key;
    unsigned long long value;

    // 总连接数
    key = 0;
    if (bpf_map_lookup_elem(policy_stats_fd, &key, &value) == 0) {
        printf("Total policy checks: %llu\n", value);
    } else {
        printf("Total policy checks: 0\n");
    }

    // 允许的连接数
    key = 1;
    if (bpf_map_lookup_elem(policy_stats_fd, &key, &value) == 0) {
        printf("Allowed connections: %llu\n", value);
    } else {
        printf("Allowed connections: 0\n");
    }

    // 拒绝的连接数
    key = 2;
    if (bpf_map_lookup_elem(policy_stats_fd, &key, &value) == 0) {
        printf("Denied connections: %llu\n", value);
    } else {
        printf("Denied connections: 0\n");
    }
}

// 主函数
int main(int argc, char **argv) {
    const char *bpf_file = "../bpf/bpf_standalone.o";
    const char *policy_file = "config/path_only_policy.yaml";
    
    // 检查root权限
    if (geteuid() != 0) {
        fprintf(stderr, "This program requires root privileges\n");
        return 1;
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    printf("=== BPF Path-Only Policy Controller (Pure C) ===\n\n");
    
    // 加载BPF程序
    if (load_bpf_program(bpf_file) < 0) {
        cleanup();
        return 1;
    }

    // 配置示例策略
    if (configure_example_policies() < 0) {
        cleanup();
        return 1;
    }

    // 显示策略摘要
    show_policy_summary();

    printf("\n✓ Path-only policy controller started successfully\n");
    printf("Note: This is a pure C implementation without BCC.\n");
    printf("      Example policies have been configured.\n");
    printf("      To attach to cgroup, use: bpftool cgroup attach <cgroup_path> connect4 pinned <prog_path>\n\n");

    printf("Press Ctrl+C to stop the policy controller...\n");
    
    // 主循环
    while (running) {
        sleep(5);
        show_statistics();
    }
    
    printf("\nPolicy controller stopped\n");
    cleanup();
    return 0;
}
