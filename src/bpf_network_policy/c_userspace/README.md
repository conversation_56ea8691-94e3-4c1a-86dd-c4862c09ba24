# BPF Path Policy Controller - Pure C Implementation

这是一个纯C语言实现的BPF网络策略控制器，不依赖BCC框架，直接使用libbpf库。

## 特性

- **纯C实现**: 不使用Python或BCC，直接使用libbpf
- **轻量级**: 最小化依赖，只需要libbpf
- **高性能**: 直接操作BPF映射，无Python解释器开销
- **进程路径控制**: 基于进程可执行文件路径的网络访问控制
- **IP地址控制**: 支持IP白名单和黑名单
- **实时统计**: 提供连接允许/拒绝统计信息

## 依赖

- libbpf-dev
- clang
- llvm
- linux-headers

## 编译和运行

### 1. 安装依赖

```bash
# Ubuntu/Debian
make install-deps

# 或手动安装
sudo apt-get install libbpf-dev clang llvm linux-headers-$(uname -r)
```

### 2. 编译

```bash
make
```

这将编译：
- eBPF程序: `../bpf/bpf_path_only_control.o`
- 用户空间程序: `path_policy_control`

### 3. 运行

```bash
# 需要root权限
sudo ./path_policy_control
```

## 示例策略

程序内置了两个示例策略：

### 策略1 - 端口8080
- **允许进程**: `/usr/bin/curl`
- **允许IP**: `127.0.0.1`
- **逻辑**: 只有curl进程且从127.0.0.1访问端口8080才被允许

### 策略2 - 端口9090  
- **拒绝进程**: `/usr/bin/wget`
- **允许IP**: `0.0.0.0` (通配符)
- **逻辑**: 除了wget进程，其他进程都可以访问端口9090

## 程序输出

```
=== BPF Path-Only Policy Controller (Pure C) ===

Loading BPF program from src/bpf_network_policy/bpf/bpf_path_only_control.o...
✓ BPF program loaded successfully
Configuring example policies...
  ✓ Configured port 8080 -> policy 1
  ✓ Added path allow: /usr/bin/curl (hash: 0x...)
  ✓ Added IP allow: 127.0.0.1 (0x7f000001)
  ✓ Configured port 9090 -> policy 2
  ✓ Added path deny: /usr/bin/wget (hash: 0x...)
  ✓ Added IP allow: 0.0.0.0 (wildcard)
✓ Example policies configured successfully

=== Policy Configuration Summary ===
Configured policies:
  Port 8080 (Policy 1):
    - Allow process: /usr/bin/curl
    - Allow IP: 127.0.0.1
  Port 9090 (Policy 2):
    - Deny process: /usr/bin/wget
    - Allow IP: 0.0.0.0 (wildcard)

✓ Path-only policy controller started successfully
Note: This is a pure C implementation without BCC.
      Example policies have been configured.
      To attach to cgroup, use: bpftool cgroup attach <cgroup_path> connect4 pinned <prog_path>

Press Ctrl+C to stop the policy controller...

=== Policy Execution Statistics ===
Total policy checks: 0
Allowed connections: 0
Denied connections: 0
```

## 附加到Cgroup

要让策略生效，需要将BPF程序附加到cgroup：

```bash
# 1. 创建测试cgroup
sudo mkdir -p /sys/fs/cgroup/test_policy

# 2. 将BPF程序附加到cgroup
sudo bpftool prog load ../bpf/bpf_path_only_control.o /sys/fs/bpf/path_policy
sudo bpftool cgroup attach /sys/fs/cgroup/test_policy connect4 pinned /sys/fs/bpf/path_policy

# 3. 将进程移动到cgroup进行测试
echo $$ | sudo tee /sys/fs/cgroup/test_policy/cgroup.procs

# 4. 测试连接（在新的shell中）
curl http://127.0.0.1:8080  # 应该被允许
wget http://127.0.0.1:9090  # 应该被拒绝
```

## 文件结构

```
c_userspace/
├── path_policy_control.c  # 主程序源码
├── Makefile              # 编译配置
└── README.md            # 本文档

../bpf/
└── bpf_path_only_control.c  # eBPF内核程序
```

## 与BCC版本的区别

| 特性 | BCC版本 | 纯C版本 |
|------|---------|---------|
| 语言 | Python + C | 纯C |
| 依赖 | BCC, Python | libbpf |
| 配置 | YAML文件 | 硬编码示例 |
| 性能 | 较低 | 较高 |
| 复杂度 | 高 | 低 |
| 可扩展性 | 高 | 中等 |

## 扩展功能

要添加更多功能，可以：

1. **添加YAML配置支持**: 集成libyaml库
2. **动态策略更新**: 添加信号处理或文件监控
3. **更多统计信息**: 扩展统计映射
4. **日志记录**: 添加详细的连接日志
5. **Web界面**: 添加HTTP API接口

## 故障排除

### 编译错误
- 确保安装了所有依赖
- 检查内核头文件版本
- 验证clang和llvm版本

### 运行时错误
- 确保以root权限运行
- 检查BPF程序是否正确编译
- 验证内核是否支持所需的BPF特性

### 策略不生效
- 确保BPF程序已附加到正确的cgroup
- 检查进程是否在目标cgroup中
- 验证策略配置是否正确
