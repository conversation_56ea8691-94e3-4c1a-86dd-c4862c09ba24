#!/bin/bash

# BPF Path Policy Controller - 测试脚本

set -e

echo "=== BPF Path Policy Controller Test Script ==="
echo

# 检查root权限
if [[ $EUID -ne 0 ]]; then
   echo "This script must be run as root"
   exit 1
fi

# 检查依赖
echo "1. Checking dependencies..."
MISSING_DEPS=()

if ! command -v clang &> /dev/null; then
    MISSING_DEPS+=("clang")
fi

if ! command -v llvm-objdump &> /dev/null; then
    MISSING_DEPS+=("llvm")
fi

if ! pkg-config --exists libbpf; then
    MISSING_DEPS+=("libbpf-dev")
fi

if [ ${#MISSING_DEPS[@]} -ne 0 ]; then
    echo "Missing dependencies: ${MISSING_DEPS[*]}"
    echo "Run 'make install-deps' to install them"
    exit 1
fi

echo "✓ All dependencies found"

# 清理之前的编译
echo
echo "2. Cleaning previous builds..."
make clean
echo "✓ Cleaned"

# 编译程序
echo
echo "3. Compiling BPF program and user space application..."
if make; then
    echo "✓ Compilation successful"
else
    echo "✗ Compilation failed"
    exit 1
fi

# 检查生成的文件
echo
echo "4. Checking generated files..."
if [[ -f "path_policy_control" ]]; then
    echo "✓ User space program: path_policy_control"
    ls -la path_policy_control
else
    echo "✗ User space program not found"
    exit 1
fi

if [[ -f "../bpf/bpf_path_only_control.o" ]]; then
    echo "✓ BPF object file: ../bpf/bpf_path_only_control.o"
    ls -la ../bpf/bpf_path_only_control.o
else
    echo "✗ BPF object file not found"
    exit 1
fi

# 检查BPF程序
echo
echo "5. Analyzing BPF program..."
make check-bpf

# 运行程序（短时间测试）
echo
echo "6. Running program test (5 seconds)..."
echo "Starting path_policy_control in background..."

# 启动程序并获取PID
./path_policy_control &
PROG_PID=$!

# 等待程序启动
sleep 2

# 检查程序是否还在运行
if kill -0 $PROG_PID 2>/dev/null; then
    echo "✓ Program is running (PID: $PROG_PID)"
    
    # 等待一段时间让程序显示统计信息
    sleep 3
    
    # 停止程序
    echo "Stopping program..."
    kill -INT $PROG_PID
    wait $PROG_PID 2>/dev/null || true
    echo "✓ Program stopped gracefully"
else
    echo "✗ Program failed to start or crashed"
    exit 1
fi

echo
echo "=== Test Summary ==="
echo "✓ Dependencies check passed"
echo "✓ Compilation successful"
echo "✓ BPF program generated correctly"
echo "✓ User space program runs without errors"
echo
echo "Next steps:"
echo "1. To run the program: sudo ./path_policy_control"
echo "2. To attach to cgroup, see README.md for instructions"
echo "3. To test policies, use curl/wget with the configured ports"
echo
echo "Test completed successfully!"
