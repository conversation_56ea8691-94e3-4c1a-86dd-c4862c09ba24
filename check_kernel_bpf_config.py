#!/usr/bin/env python3
"""
检查内核BPF配置工具

检查内核是否启用了所需的BPF配置选项
"""

import os
import sys
import subprocess
import glob

def find_kernel_config():
    """查找内核配置文件"""
    print("=== 查找内核配置文件 ===")
    
    kernel_version = subprocess.check_output(['uname', '-r']).decode().strip()
    print(f"当前内核版本: {kernel_version}")
    
    # 可能的配置文件位置
    config_paths = [
        f"/boot/config-{kernel_version}",
        "/proc/config.gz",
        f"/usr/src/linux-{kernel_version}/.config",
        "/usr/src/linux/.config"
    ]
    
    for config_path in config_paths:
        if os.path.exists(config_path):
            print(f"✅ 找到配置文件: {config_path}")
            return config_path
    
    # 尝试查找任何config文件
    boot_configs = glob.glob("/boot/config-*")
    if boot_configs:
        config_path = boot_configs[0]
        print(f"✅ 找到配置文件: {config_path}")
        return config_path
    
    print("❌ 未找到内核配置文件")
    return None

def read_kernel_config(config_path):
    """读取内核配置"""
    print(f"\n=== 读取内核配置: {config_path} ===")
    
    try:
        if config_path.endswith('.gz'):
            # 处理压缩的配置文件
            import gzip
            with gzip.open(config_path, 'rt') as f:
                config_content = f.read()
        else:
            # 处理普通文本文件
            with open(config_path, 'r') as f:
                config_content = f.read()
        
        print(f"✅ 成功读取配置文件 ({len(config_content)} 字符)")
        return config_content
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return None

def check_bpf_configs(config_content):
    """检查BPF相关配置"""
    print("\n=== 检查BPF相关配置 ===")
    
    # 关键的BPF配置选项
    critical_configs = {
        'CONFIG_BPF': 'BPF基础支持',
        'CONFIG_BPF_SYSCALL': 'BPF系统调用支持',
        'CONFIG_CGROUP_BPF': 'cgroup BPF支持 (关键)',
        'CONFIG_BPF_EVENTS': 'BPF事件支持',
        'CONFIG_SOCK_CGROUP_DATA': 'Socket cgroup数据支持',
    }
    
    # 重要的BPF配置选项
    important_configs = {
        'CONFIG_BPF_JIT': 'BPF JIT编译器',
        'CONFIG_BPF_JIT_ALWAYS_ON': 'BPF JIT总是开启',
        'CONFIG_BPF_LSM': 'BPF LSM支持',
        'CONFIG_NET_CLS_BPF': '网络分类器BPF',
        'CONFIG_NET_ACT_BPF': '网络动作BPF',
        'CONFIG_BPF_STREAM_PARSER': 'BPF流解析器',
        'CONFIG_LWTUNNEL_BPF': '轻量级隧道BPF',
    }
    
    # 可选的BPF配置选项
    optional_configs = {
        'CONFIG_BPF_KPROBE_OVERRIDE': 'BPF kprobe覆盖',
        'CONFIG_BPF_UNPRIV_DEFAULT_OFF': 'BPF非特权默认关闭',
        'CONFIG_TEST_BPF': 'BPF测试模块',
        'CONFIG_NETFILTER_XT_MATCH_BPF': 'Netfilter BPF匹配',
        'CONFIG_BPFILTER': 'BPF过滤器',
        'CONFIG_IPV6_SEG6_BPF': 'IPv6段路由BPF',
    }
    
    def check_config_group(configs, group_name, critical=False):
        print(f"\n{group_name}:")
        all_enabled = True
        
        for config, description in configs.items():
            if f"{config}=y" in config_content:
                status = "✅ 启用"
                enabled = True
            elif f"{config}=m" in config_content:
                status = "🔶 模块"
                enabled = True
            elif f"# {config} is not set" in config_content:
                status = "❌ 禁用"
                enabled = False
            else:
                status = "❓ 未找到"
                enabled = False
            
            if critical and not enabled:
                all_enabled = False
            
            print(f"  {config:<30} {status:<8} {description}")
        
        return all_enabled
    
    # 检查各组配置
    critical_ok = check_config_group(critical_configs, "关键配置", critical=True)
    check_config_group(important_configs, "重要配置")
    check_config_group(optional_configs, "可选配置")
    
    return critical_ok

def check_runtime_bpf_support():
    """检查运行时BPF支持"""
    print("\n=== 检查运行时BPF支持 ===")
    
    checks = []
    
    # 检查BPF JIT
    try:
        with open('/proc/sys/net/core/bpf_jit_enable', 'r') as f:
            jit_enabled = f.read().strip()
        if jit_enabled == '1':
            print("✅ BPF JIT已启用")
            checks.append(True)
        else:
            print("❌ BPF JIT未启用")
            checks.append(False)
    except:
        print("❓ 无法检查BPF JIT状态")
        checks.append(False)
    
    # 检查BPF文件系统
    try:
        result = subprocess.run(['mount'], capture_output=True, text=True)
        if 'bpffs' in result.stdout:
            print("✅ BPF文件系统已挂载")
            checks.append(True)
        else:
            print("⚠️  BPF文件系统未挂载")
            checks.append(False)
    except:
        print("❓ 无法检查BPF文件系统")
        checks.append(False)
    
    # 检查cgroup v2
    try:
        result = subprocess.run(['mount'], capture_output=True, text=True)
        if 'cgroup2' in result.stdout:
            print("✅ cgroup v2已挂载")
            checks.append(True)
        else:
            print("⚠️  cgroup v2未挂载")
            checks.append(False)
    except:
        print("❓ 无法检查cgroup挂载")
        checks.append(False)
    
    # 检查cgroup控制器
    try:
        with open('/sys/fs/cgroup/cgroup.controllers', 'r') as f:
            controllers = f.read().strip()
        print(f"✅ cgroup控制器: {controllers}")
        checks.append(True)
    except:
        print("❓ 无法检查cgroup控制器")
        checks.append(False)
    
    return all(checks)

def test_simple_bpf():
    """测试简单的BPF程序加载"""
    print("\n=== 测试简单BPF程序加载 ===")
    
    try:
        from bcc import BPF
        
        # 最简单的BPF程序
        simple_code = """
int hello(void *ctx) {
    return 0;
}
"""
        
        print("测试基本BPF程序编译...")
        bpf = BPF(text=simple_code)
        print("✅ 基本BPF程序编译成功")
        
        # 测试kprobe加载
        try:
            fn = bpf.load_func("hello", BPF.KPROBE)
            print("✅ kprobe BPF程序加载成功")
            return True
        except Exception as e:
            print(f"❌ kprobe BPF程序加载失败: {e}")
            return False
            
    except ImportError:
        print("❌ BCC未安装")
        return False
    except Exception as e:
        print(f"❌ BPF测试失败: {e}")
        return False

def provide_diagnosis():
    """提供诊断结果"""
    print("\n=== 诊断结果 ===")
    
    print("基于以上检查结果，cgroup BPF无法加载的可能原因:")
    
    print("\n1. 如果关键配置都已启用但仍然失败:")
    print("   - 可能是BPF验证器过于严格")
    print("   - 可能是函数签名问题")
    print("   - 可能是内核版本特定的bug")
    
    print("\n2. 如果某些关键配置缺失:")
    print("   - 需要重新编译内核")
    print("   - 或者使用预编译的支持BPF的内核")
    
    print("\n3. 替代解决方案:")
    print("   - 使用XDP进行网络拦截")
    print("   - 使用TC BPF进行流量控制")
    print("   - 使用LSM BPF进行安全控制")
    print("   - 使用netfilter/iptables集成")
    
    print("\n4. 调试建议:")
    print("   - 查看dmesg中的BPF相关错误")
    print("   - 使用strace跟踪BPF系统调用")
    print("   - 尝试更简单的BPF程序类型")

def main():
    """主函数"""
    print("内核BPF配置检查工具")
    print("=" * 50)
    
    # 1. 查找配置文件
    config_path = find_kernel_config()
    if not config_path:
        print("无法找到内核配置文件，无法进行详细检查")
        return 1
    
    # 2. 读取配置
    config_content = read_kernel_config(config_path)
    if not config_content:
        return 1
    
    # 3. 检查BPF配置
    critical_ok = check_bpf_configs(config_content)
    
    # 4. 检查运行时支持
    runtime_ok = check_runtime_bpf_support()
    
    # 5. 测试BPF加载
    bpf_ok = test_simple_bpf()
    
    # 6. 提供诊断
    provide_diagnosis()
    
    # 总结
    print("\n" + "=" * 50)
    print("总结:")
    print(f"关键配置: {'✅ 正常' if critical_ok else '❌ 有问题'}")
    print(f"运行时支持: {'✅ 正常' if runtime_ok else '❌ 有问题'}")
    print(f"BPF基本功能: {'✅ 正常' if bpf_ok else '❌ 有问题'}")
    
    if critical_ok and runtime_ok and bpf_ok:
        print("\n🎉 内核BPF支持正常，cgroup BPF问题可能是程序特定的")
    else:
        print("\n⚠️  内核BPF支持存在问题，需要进一步排查")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
